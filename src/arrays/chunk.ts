/**
 * Splits an iterable into chunks of specified size.
 * Returns an iterable of chunks for optimal memory usage with large inputs.
 *
 * @param iterable - The iterable to chunk
 * @param size - The size of each chunk (must be positive integer)
 * @returns An iterable iterator that yields arrays of the specified size
 * @throws {Error} When size is not a positive integer
 *
 * @example
 * ```typescript
 * // Basic usage
 * const chunks = chunk([1, 2, 3, 4, 5], 2);
 * console.log([...chunks]); // [[1, 2], [3, 4], [5]]
 *
 * // Memory efficient for large datasets
 * const largeArray = Array.from({ length: 1000000 }, (_, i) => i);
 * for (const chunk of chunk(largeArray, 1000)) {
 *   // Process each chunk without loading all chunks into memory
 *   console.log(chunk.length); // 1000 (except possibly the last chunk)
 * }
 *
 * // Works with any iterable
 * const set = new Set([1, 2, 3, 4]);
 * const setChunks = chunk(set, 2);
 * console.log([...setChunks]); // [[1, 2], [3, 4]]
 * ```
 */
export function * chunk<T>(iterable: Iterable<T>, size: number): IterableIterator<T[]> {
    if (!Number.isInteger(size) || size <= 0) {
        throw new Error('Chunk size must be a positive integer')
    }

    const iterator = iterable[Symbol.iterator]()
    let result = iterator.next()

    while (!result.done) {
        const chunk: T[] = []

        // Fill the current chunk up to the specified size
        for (let i = 0; i < size && !result.done; i++) {
            chunk.push(result.value)
            result = iterator.next()
        }

        // Yield the chunk if it has any elements
        if (chunk.length > 0) {
            yield chunk
        }
    }
}
